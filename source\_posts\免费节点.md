---
title: 免费节点
date: 2025-06-27 01:28:39
categories: 
- 网络工具
tags: 
- 代理
- 节点
cover: https://cdn4.winhlb.com/2025/06/27/685d830497f1e.jpeg
---

## 免责声明

> 本文档仅用于学习和教育目的，旨在帮助安全研究人员和开发者了解网络技术。本博文中所包含的信息和工具仅用于合法的安全测试和研究，不得用于任何非法活动。
>
> 使用本文所提供的信息进行任何未经授权的行为均为非法行为，违反法律将导致严重的法律后果。读者在使用这些信息时，必须确保拥有合法的授权，并严格遵守所在国家和地区的法律法规。
>
> 作者不对任何因使用本文内容而导致的直接或间接损害承担责任。所有风险和责任由用户自行承担。
>
> 此外，读者必须在24小时内删除产生的内容，以确保信息不会被滥用。

## 节点列表

### 1. atfreecloudltd 节点
**国家**: 意大利
**类型**: V2Ray 节点

#### 安装脚本
```bash
vmpt="" argo="y" agn="" agk="" bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh)
```

#### 示例配置
```bash
vmpt="11005" argo="y" agn="sealos.0407123.xyz" agk="eyJhIjoiY2YxNzgzMzU3ODliNjA0ZTc5N2NjMmEwMTZiZGU4YzEiLCJ0IjoiNTM4YmEwYTMtM2JhZi00Y2FlLTk0Y2YtOGJiM2ZiMGFjMTY0IiwicyI6Ik5EaG1NV0poWTJNdFpHVTVOQzAwTURWa0xUazBaRFV0TUdKa05qUXhZalUyTURWaiJ9" bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh)
```

#### 节点配置
```
vmess://eyAidiI6ICIyIiwgInBzIjogInZtLXdzLXNlcjEzMDA3MTM3MzA3NSIsICJhZGQiOiAiMTUyLjg5LjE3MC4xODciLCAicG9ydCI6ICIxMTAwNSIsICJpZCI6ICI0ZDg4NTIwYi05OWY3LTRiNjEtYTJlZi00NzJmYmFjMGUxOWMiLCAiYWlkIjogIjAiLCAic2N5IjogImF1dG8iLCAibmV0IjogIndzIiwgInR5cGUiOiAibm9uZSIsICJob3N0IjogInd3dy5iaW5nLmNvbSIsICJwYXRoIjogIi80ZDg4NTIwYi05OWY3LTRiNjEtYTJlZi00NzJmYmFjMGUxOWMtdm0/ZWQ9MjA0OCIsICJ0bHMiOiAiIn0K
```

#### 常用命令
1. 查看Argo信息：
   ```bash
   agsb list
   # 或
   bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) list
   ```

2. 切换IP协议：
   - 显示IPv4配置：
     ```bash
     ip=4 agsb list
     ```
   - 显示IPv6配置：
     ```bash
     ip=6 agsb list
     ```

3. 卸载脚本：
   ```bash
   agsb del
   # 或
   bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) del
   ```
1、查看Argo的固定域名、固定隧道的代币、临时域名、当前已安装的节点信息：

agsb list或者bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) list

2、在线切换IPV4/IPV6节点配置（双栈VPS专享）：

显示IPV4节点配置：

ip=4 agsb list或者ip=4 bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) list

显示IPV6节点配置：

ip=6 agsb list或者ip=6 bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) list

3、卸载脚本：

agsb del或者bash <(curl -Ls https://raw.githubusercontent.com/zzzhhh1/argosb/main/argosb.sh) del   

---

### 2. webhostmost 代理节点（三网优化）

#### 主要节点
- **美国节点**:
  [http://7.0407123.xyz/aa75aba0-a3a3-41d8-ac6a-7a2323df048d](http://7.0407123.xyz/aa75aba0-a3a3-41d8-ac6a-7a2323df048d)
- **印度节点**:
  [https://1yd.0407123.xyz/6d908c5b-4621-44ab-811c-05e056fadd23](https://1yd.0407123.xyz/6d908c5b-4621-44ab-811c-05e056fadd23)
- **荷兰备用**:
  [https://bls.qianxiu.dpdns.org/sub](https://bls.qianxiu.dpdns.org/sub)

#### 备用节点
> 当上述节点失效时使用

- **管理面板**:
  [https://2.0407123.xyz/panel](https://2.0407123.xyz/panel)
- **登录密码**: `Ch123456789`