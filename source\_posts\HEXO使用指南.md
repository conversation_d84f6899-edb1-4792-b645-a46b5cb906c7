---
title: 搭建Hexo博客
cover: https://cdn4.winhlb.com/2025/06/08/68449247315f0.jpeg
date: 2025-06-08
categories: [技术教程]
tags: [Hexo, 博客搭建, 静态博客]
---

# 搭建Hexo博客

快速简洁高效，零成本搭建个人博客：Hexo + GitHub Pages + Cloudflare Pages 完整指南

本文详细介绍了如何使用Hexo框架搭建一个个人博客，并将其部署到GitHub Pages和Cloudflare Pages上。

## 主要内容包括

- 环境准备：安装Node.js和Git
- 配置Git和GitHub：设置SSH密钥，创建GitHub仓库
- 初始化Hexo项目：安装Hexo，创建新博客
- 部署到GitHub Pages：配置部署设置，推送静态文件
- 部署到Cloudflare Pages：连接GitHub仓库，自动部署
- 基本使用方法：创建新文章，本地预览，发布更新

这个教程适合那些想要快速搭建个人博客，但又不想花费太多成本的人。通过使用Hexo、GitHub和Cloudflare的免费服务，您可以轻松创建一个高效、简洁的博客网站。

## 1. 事前准备

- 域名（非必须，你也可以使用免费域名，或者GitHub.io或Pages.dev分配的域名也可以）
- GitHub（必须，你需要注册一个GitHub帐号）
- Cloudflare（非必须，你需要注册一个Cloudflare帐号，这样你就可以将博客部署在CF的CDN里加速，但是你也可以直接使用GitHub.io分配的域名）

## 2. 软件支持

- Node（必须）
- Git（必须）
- VSCode（非必须，这是一款轻量型的代码编辑器，可以帮助你养成一个很好的编程习惯）

### 2.1 安装 Node

1. 打开Node官网，下载和自己系统相配的Node的安装程序，否则会出现安装问题。下载地址：[https://nodejs.org/en](https://nodejs.org/en)
2. 下载后安装，安装的目录可以使用默认目录`C:/Program Files/nodejs/`
3. 安装完成后，检查是否安装成功。在键盘按下`win + R`键，输入`CMD`，然后回车，打开CMD窗口，执行`node -v`命令，看到版本信息，则说明安装成功。

#### 2.1.3 修改npm源

npm下载各种模块，默认是从国处服务器下载，速度较慢，建议配置成华为云镜像源。打开CMD窗口，运行如下命令:

```bash
npm config set registry https://mirrors.huaweicloud.com/repository/npm/
```

### 2.2 安装 Git

1. 进入官网下载适合你当前系统的 Git：[https://git-scm.com/downloads](https://git-scm.com/downloads)
2. 下载后傻瓜式安装Git即可，安装的目录最好使用默认目录`C:/Program Files/Git`
3. 点击电脑左下角开始即可看见Git CMD、Git Bash、Git GUI。
   - Git CMD 是windows 命令行的指令风格
   - Git Bash 是linux系统的指令风格（建议使用）
   - Git GUI是图形化界面（新手学习不建议使用）

## 3. 配置 Git 密钥并连接至 Github

常用 Git 命令

```bash
git config -l  //查看所有配置
git config --system --list //查看系统配置
git config --global --list //查看用户（全局）配置
```

### 3.1 配置用户名和邮箱

```bash
git config --global user.name "你的用户名"
git config --global user.email "你的邮箱"
```

通过`git config -l` 检查是否配置成功。

### 3.2 配置公钥连接Github

1. 执行以下命令生成ssh公钥，此公钥用于你的计算机连接Github

```bash
ssh-keygen -t rsa -C "你的邮箱"
```

提示`Enter file in which to save the key`直接一路回车即可，新手小白不推荐设置密钥

2. 之后打开C盘下用户文件夹下的`.ssh`的文件夹，会看到以下文件
   - `id_rsa`私钥
   - `id_rsa.pub`公钥
3. 用记事本打开上述图片中的公钥`id_rsa.pub`，复制里面的内容
4. 将 SSH KEY 配置到 GitHub
   - 进入github，点击右上角头像 选择settings
   - 进入设置页后选择 SSH and GPG keys
   - 名字随便起，公钥填到Key那一栏
5. 测试连接，输入以下命令

```bash
ssh -T **************
```

第一次连接会提示`Are you sure you want to continue connecting (yes/no/[fingerprint])?`，输入`yes`即可

出现连接到账户的信息，说明已经大功告成，至此完成了环境准备工作。

### 3.3 创建GitHub.io仓库

1. 点击右上角的`+`按钮，选择`New repository`
2. 创建一个`<用户名>.github.io`的仓库
3. 仓库名字的格式必须为：`<用户名>.github.io` (注意：前缀必须为用户名，此为预览博客需要，后期可修改仓库名)
4. 可见性必须选择 Public 方便第一次部署检查问题
5. 点击 Creat repository 进行创建即可

## 4. 初始化 Hexo 博客

1. 创建一个文件夹来保存博客源码（我这里选的路径为`D:/Hexo-Blog`），在文件夹内右键鼠标，选择`Open Git Bash here`
2. 在Git BASH输入如下命令安装 Hexo

```bash
npm install -g hexo-cli && hexo -v
```

3. 安装完后输入`hexo -v`验证是否安装成功
4. 初始化 Hexo 项目安装相关依赖

```bash
hexo init blog-demo
cd blog-demo
npm i
```

5. 初始化项目后，`blog-demo`有如下结构：
   - `node_modules`：依赖包
   - `scaffolds`：生成文章的一些模板
   - `source`：用来存放你的文章
   - `themes`：主题
   - `.npmignore`：发布时忽略的文件（可忽略）
   - `_config.landscape.yml`：主题的配置文件
   - `_config.yml`：博客的配置文件
   - `package.json`：项目名称、描述、版本、运行和开发等信息
6. 输入`hexo cl && hexo s`启动项目
7. 打开浏览器，输入地址：[http://localhost:4000/](http://localhost:4000/) ，看到下面的效果，说明你的博客已经构建成功了

## 5. 将静态博客挂载到 GitHub Pages

1. 安装 hexo-deployer-git

```bash
npm install hexo-deployer-git --save
```

2. 修改 `_config.yml` 文件

在`blog-demo`目录下的`_config.yml`，就是整个Hexo框架的配置文件了。可以在里面修改大部分的配置。详细可参考官方的配置描述。

修改最后一行的配置，将`repository`修改为你自己的github项目地址即可，还有分支要改为`main`代表主分支（注意缩进）。

```yaml
deploy:
  type: git
  repository: **************:cmliussss2024/cmliussss2024.github.io.git
  branch: main
```

3. 修改好配置后，运行如下命令，将代码部署到 GitHub（Hexo三连）

```bash
# Git BASH终端
hexo clean && hexo generate && hexo deploy  

# 或者

# VSCODE终端
hexo cl; hexo g; hexo d
```

- `hexo clean`：删除之前生成的文件，可以用`hexo cl`缩写
- `hexo generate`：生成静态文章，可以用`hexo g`缩写
- `hexo deploy`：部署文章，可以用`hexo d`缩写

注意：`deploy`时可能要你输入 username 和 password。

如果出现`Deploy done`，则说明部署成功了。

稍等两分钟，打开浏览器访问：[https://cmliussss2024.github.io](https://cmliussss2024.github.io) ，这时候我们就可以看到博客内容了。

## 6. 将静态博客挂载到 Cloudflare Pages

1. 在 Workers 和 Pages 中选择 Pages 的 连接到 Git
2. 然后登录你Blog仓库对应的GitHub帐号
3. 点击保存并部署后等待部署完成即可

提示`成功！您的项目已部署到以下区域：全球`后，浏览器访问：[https://cmliussss2024-github-io.pages.dev](https://cmliussss2024-github-io.pages.dev) ，这时候我们就可以看到博客内容了。

这时你也就可以将你的`<用户名>.github.io`的仓库设置为Private私库了

如果你有自己的域名，你可以在这里绑定你自己的自定义域，即可

## 如何使用

### 新建一篇博文

```bash
hexo new 这是一篇新的博文
```

然后用文本编辑器去编辑`_posts/这是一篇新的博文.md`里的内容即可，注意要使用Markdown格式书写。

详细使用方法可以查阅 [Hexo官方文档](https://hexo.io/zh-cn/docs/writing)

### 编辑和预览

编辑完文章保存后可以使用如下命令，生成本地页面 [http://localhost:4000/](http://localhost:4000/) ，进行预览

```bash
# Git BASH终端
hexo cl && hexo s

# 或者

# VSCODE终端
hexo cl; hexo s
```

确认无误后使用以下命令，将本地文章推送至GitHub仓库即可

```bash
# Git BASH终端
hexo cl && hexo g && hexo d

# 或者

# VSCODE终端
hexo cl; hexo g; hexo d
```

下一期会讲讲进阶的使用方法，主题美化

## VSCODE 终端首次执行报错

使用管理员身份打开 powershell ,输入以下命令

```powershell
Set-ExecutionPolicy RemoteSigned
```

## 免责声明

本文档仅用于学习和教育目的，旨在帮助安全研究人员和开发者了解。本博文中所包含的信息和工具仅用于合法的安全测试和研究，不得用于任何非法活动。

使用本文所提供的信息进行任何未经授权的行为均为非法行为，违反法律将导致严重的法律后果。读者在使用这些信息时，必须确保拥有合法的授权，并严格遵守所在国家和地区的法律法规。

作者不对任何因使用本文内容而导致的直接或间接损害承担责任。所有风险和责任由用户自行承担。

此外，读者必须在24小时内删除产生的内容，以确保信息不会被滥用。

## 参考资料

- [Hexo官方文档](https://hexo.io/zh-cn/)
- [Fomal的Hexo教程](https://www.fomal.cc/posts/e593433d.html)
- [Anheyu文档](https://docs.anheyu.com/)